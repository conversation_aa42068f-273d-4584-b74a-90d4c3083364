<template>
  <view class="container">
    <!-- 轮播图 -->
    <swiper class="swiper" autoplay interval="3000">
      <swiper-item v-for="(item, index) in banners" :key="index">
        <image :src="item.imgUrl" mode="aspectFill" />
      </swiper-item>
    </swiper>

    <!-- 服务分类 -->
    <view class="service-category">
      <view v-for="(item, index) in categories" :key="index" class="category-item">
        <image :src="item.icon" class="category-icon" />
        <text>{{ item.name }}</text>
      </view>
    </view>

    <!-- 推荐服务 -->
    <view class="recommend-section">
      <view class="section-title">热门婚礼服务</view>
      <scroll-view scroll-x class="scroll-list">
        <view v-for="(item, index) in recommends" :key="index" class="service-card">
          <image :src="item.cover" class="service-image" />
          <text class="service-name">{{ item.name }}</text>
          <text class="service-price">¥{{ item.price }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      banners: [
        { imgUrl: '/static/images/banner1.jpg' },
        { imgUrl: '/static/images/banner2.jpg' }
      ],
      categories: [
        { name: '婚纱摄影', icon: '/static/images/camera-icon.png' },
        { name: '婚庆公司', icon: '/static/images/company-icon.png' },
        { name: '婚礼跟拍', icon: '/static/images/video-icon.png' },
        { name: '婚纱礼服', icon: '/static/images/dress-icon.png' }
      ],
      recommends: [
        { name: '海岛婚礼套餐', price: '68888', cover: '/static/images/service1.jpg' },
        { name: '中式传统婚礼', price: '98888', cover: '/static/images/service2.jpg' }
      ]
    }
  }
}
</script>

<style lang="scss">
.swiper {
  height: 360rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

.service-category {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
  .category-item {
    text-align: center;
    image {
      width: 80rpx;
      height: 80rpx;
    }
  }
}

.recommend-section {
  padding: 20rpx;
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  .scroll-list {
    white-space: nowrap;
    .service-card {
      display: inline-block;
      width: 280rpx;
      margin-right: 20rpx;
      .service-image {
        width: 100%;
        height: 200rpx;
        border-radius: 10rpx;
      }
    }
  }
}
</style>
